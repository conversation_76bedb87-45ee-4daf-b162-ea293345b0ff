# 🌤️ Akaal Weather - ਅਕਾਲ ਮੌਸਮ

A beautiful weather app with animations that displays weather forecasts in Punjabi language.

## ✨ Features

- 🎨 **Beautiful Animations**: Animated weather icons with rotation, pulse, and bounce effects
- 🌈 **Dynamic Backgrounds**: Gradient backgrounds that change based on weather conditions
- 🇮🇳 **Punjabi Language**: All weather information displayed in Punjabi with natural language descriptions
- 📱 **Modern UI**: Clean, modern interface with smooth animations
- 🌍 **Location-based**: Automatic location detection for accurate weather data
- 📊 **Detailed Information**: Temperature, humidity, wind speed, rain probability, and more

## 🚀 Setup Instructions

### 1. Prerequisites
- Flutter SDK (latest stable version)
- Android Studio / VS Code
- Tomorrow.io API key (free at https://www.tomorrow.io/)

### 2. Installation
```bash
# Clone the repository
git clone <your-repo-url>
cd akaal_weather

# Install dependencies
flutter pub get

# Run the app
flutter run
```

### 3. API Configuration
1. Get your free API key from [Tomorrow.io](https://www.tomorrow.io/)
2. Open `lib/services/weather_service.dart`
3. Replace `'abc'` with your actual API key:
```dart
static const String _apiKey = 'your_actual_api_key_here';
```

## 📱 Permissions

The app requires the following permissions:
- **Location**: To get weather data for your current location
- **Internet**: To fetch weather data from the API

## 🏗️ Project Structure

```
lib/
├── main.dart                           # App entry point
├── models/
│   └── weather_model.dart             # Weather data models
├── services/
│   ├── weather_service.dart           # Tomorrow.io API integration
│   └── punjabi_translator.dart        # Punjabi translations
├── widgets/
│   ├── animated_weather_icon.dart     # Animated weather icons
│   └── weather_card.dart              # Weather display cards
├── screens/
│   └── weather_screen.dart            # Main weather screen
└── utils/
    └── weather_utils.dart             # Utility functions
```

## 🌟 Example Outputs

The app generates natural language descriptions in Punjabi:

- "ਅੱਜ, ਦੁਪਹਿਰ ਨੂੰ 70% ਸੰਭਾਵਨਾ ਨਾਲ ਮੀਂਹ ਪਵੇਗਾ।"
- "ਅੱਜ ਸਾਰਾ ਦਿਨ ਚੰਗੀ ਹਵਾ ਚਲੇਗੀ। ਹਵਾ ਦੀ ਗਤੀ = 25km/h, ਦਿਸ਼ਾ E।"
- "ਅੱਜ ਸਵੇਰੇ ਬਹੁਤ ਜ਼ਿਆਦਾ ਨਮੀ ਹੋਵੇਗੀ, ਪਰ ਦੁਪਹਿਰ ਨੂੰ ਬੱਦਲ ਆਉਣਗੇ।"

## 🧪 Testing

Run tests with:
```bash
flutter test
```

## 📄 License

This project is licensed under the MIT License.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
