import '../models/weather_model.dart';

/// Service to translate weather conditions and generate natural language descriptions in Punjabi
class PunjabiTranslator {
  
  /// Generate natural language weather description in Punjabi
  static String generateWeatherDescription(WeatherForecast forecast) {
    final weather = forecast.weatherData;
    final dayName = forecast.dayName;
    
    List<String> descriptions = [];
    
    // Temperature description
    String tempDesc = _getTemperatureDescription(weather.temperature);
    
    // Rain description
    if (weather.precipitationProbability > 60) {
      String rainDesc = _getRainDescription(weather.precipitationProbability, weather.precipitationIntensity);
      descriptions.add(rainDesc);
    }
    
    // Wind description
    if (weather.windSpeed > 15) {
      String windDesc = _getWindDescription(weather.windSpeed, weather.windDirectionPunjabi);
      descriptions.add(windDesc);
    }
    
    // Humidity description
    if (weather.humidity > 70) {
      descriptions.add(_getHumidityDescription(weather.humidity));
    }
    
    // Cloud description
    String cloudDesc = _getCloudDescription(weather.weatherCode, weather.cloudCover);
    if (cloudDesc.isNotEmpty) {
      descriptions.add(cloudDesc);
    }
    
    // Combine descriptions
    String mainDescription = descriptions.isNotEmpty 
        ? descriptions.join(', ਅਤੇ ') 
        : _getBasicWeatherDescription(weather.weatherCode);
    
    return '$dayName, $tempDesc। $mainDescription।';
  }
  
  /// Generate detailed weather information in Punjabi
  static String generateDetailedDescription(WeatherForecast forecast) {
    final weather = forecast.weatherData;
    List<String> details = [];
    
    // Rain chance
    if (weather.precipitationProbability > 20) {
      details.add('ਮੀਂਹ ਦੀ ਸੰਭਾਵਨਾ = ${weather.precipitationProbability.round()}%');
    }
    
    // Wind details
    if (weather.windSpeed > 10) {
      details.add('ਹਵਾ ਦੀ ਗਤੀ = ${weather.windSpeed.round()}km/h, ਦਿਸ਼ਾ ${weather.windDirectionAbbr}');
    }
    
    // Humidity
    if (weather.humidity > 60) {
      details.add('ਨਮੀ = ${weather.humidity.round()}%');
    }
    
    return details.join('\n');
  }
  
  /// Get temperature description in Punjabi
  static String _getTemperatureDescription(double temp) {
    if (temp < 10) {
      return 'ਬਹੁਤ ਠੰਡ ਹੈ (${temp.round()}°C)';
    } else if (temp < 20) {
      return 'ਠੰਡ ਹੈ (${temp.round()}°C)';
    } else if (temp < 30) {
      return 'ਸੁਹਾਵਣਾ ਮੌਸਮ ਹੈ (${temp.round()}°C)';
    } else if (temp < 35) {
      return 'ਗਰਮ ਹੈ (${temp.round()}°C)';
    } else {
      return 'ਬਹੁਤ ਗਰਮ ਹੈ (${temp.round()}°C)';
    }
  }
  
  /// Get rain description in Punjabi
  static String _getRainDescription(double probability, double intensity) {
    String chanceText;
    if (probability >= 80) {
      chanceText = 'ਪੱਕੀ ਤੌਰ ਤੇ';
    } else if (probability >= 60) {
      chanceText = '${probability.round()}% ਸੰਭਾਵਨਾ ਨਾਲ';
    } else {
      chanceText = '${probability.round()}% ਸੰਭਾਵਨਾ ਨਾਲ';
    }
    
    String intensityText;
    if (intensity > 10) {
      intensityText = 'ਤੇਜ਼ ਮੀਂਹ';
    } else if (intensity > 2) {
      intensityText = 'ਮੀਂਹ';
    } else {
      intensityText = 'ਹਲਕਾ ਮੀਂਹ';
    }
    
    return '$chanceText $intensityText ਪਵੇਗਾ';
  }
  
  /// Get wind description in Punjabi
  static String _getWindDescription(double speed, String direction) {
    String speedText;
    if (speed > 40) {
      speedText = 'ਤੇਜ਼ ਤੂਫਾਨੀ ਹਵਾ';
    } else if (speed > 25) {
      speedText = 'ਤੇਜ਼ ਹਵਾ';
    } else {
      speedText = 'ਚੰਗੀ ਹਵਾ';
    }
    
    return '$speedText $direction ਦਿਸ਼ਾ ਤੋਂ ਚਲੇਗੀ';
  }
  
  /// Get humidity description in Punjabi
  static String _getHumidityDescription(double humidity) {
    if (humidity > 85) {
      return 'ਬਹੁਤ ਜ਼ਿਆਦਾ ਨਮੀ ਹੋਵੇਗੀ';
    } else if (humidity > 70) {
      return 'ਜ਼ਿਆਦਾ ਨਮੀ ਹੋਵੇਗੀ';
    } else {
      return 'ਨਮੀ ਹੋਵੇਗੀ';
    }
  }
  
  /// Get cloud description in Punjabi
  static String _getCloudDescription(int weatherCode, double cloudCover) {
    switch (weatherCode) {
      case 1000: // Clear
        return 'ਸਾਫ਼ ਅਸਮਾਨ ਹੋਵੇਗਾ';
      case 1001: // Cloudy
        return 'ਬੱਦਲ ਛਾਏ ਰਹਿਣਗੇ';
      case 1100: // Mostly Clear
        return 'ਜ਼ਿਆਦਾਤਰ ਸਾਫ਼ ਅਸਮਾਨ ਹੋਵੇਗਾ';
      case 1101: // Partly Cloudy
        return 'ਕੁਝ ਬੱਦਲ ਹੋਣਗੇ';
      case 1102: // Mostly Cloudy
        return 'ਜ਼ਿਆਦਾਤਰ ਬੱਦਲ ਹੋਣਗੇ';
      case 2000: // Fog
      case 2100: // Light Fog
        return 'ਧੁੰਦ ਹੋਵੇਗੀ';
      default:
        if (cloudCover > 70) {
          return 'ਬੱਦਲ ਛਾਏ ਰਹਿਣਗੇ';
        } else if (cloudCover > 30) {
          return 'ਕੁਝ ਬੱਦਲ ਹੋਣਗੇ';
        }
        return '';
    }
  }
  
  /// Get basic weather description for weather codes
  static String _getBasicWeatherDescription(int weatherCode) {
    switch (weatherCode) {
      case 1000:
        return 'ਸਾਫ਼ ਅਤੇ ਸੁੰਦਰ ਮੌਸਮ ਹੋਵੇਗਾ';
      case 1001:
        return 'ਬੱਦਲਵਾਈ ਰਹੇਗੀ';
      case 4001:
        return 'ਮੀਂਹ ਪਵੇਗਾ';
      case 5000:
        return 'ਬਰਫ਼ ਪਵੇਗੀ';
      case 8000:
        return 'ਤੂਫਾਨ ਆਵੇਗਾ';
      default:
        return 'ਮੌਸਮ ਬਦਲਦਾ ਰਹੇਗਾ';
    }
  }
  
  /// Get time-specific descriptions
  static String getTimeSpecificDescription(List<HourlyWeather> hourlyData, String dayName) {
    if (hourlyData.isEmpty) return '';
    
    List<String> timeDescriptions = [];
    
    // Morning (6 AM - 12 PM)
    final morningData = hourlyData.where((h) => h.time.hour >= 6 && h.time.hour < 12).toList();
    if (morningData.isNotEmpty) {
      final avgRain = morningData.map((h) => h.precipitationProbability).reduce((a, b) => a + b) / morningData.length;
      final avgHumidity = morningData.map((h) => h.humidity).reduce((a, b) => a + b) / morningData.length;
      
      if (avgRain > 60) {
        timeDescriptions.add('$dayName ਸਵੇਰੇ ਮੀਂਹ ਪਵੇਗਾ');
      } else if (avgHumidity > 80) {
        timeDescriptions.add('$dayName ਸਵੇਰੇ ਬਹੁਤ ਨਮੀ ਹੋਵੇਗੀ');
      }
    }
    
    // Afternoon (12 PM - 6 PM)
    final afternoonData = hourlyData.where((h) => h.time.hour >= 12 && h.time.hour < 18).toList();
    if (afternoonData.isNotEmpty) {
      final avgRain = afternoonData.map((h) => h.precipitationProbability).reduce((a, b) => a + b) / afternoonData.length;
      final avgWind = afternoonData.map((h) => h.windSpeed).reduce((a, b) => a + b) / afternoonData.length;
      
      if (avgRain > 60) {
        timeDescriptions.add('ਦੁਪਹਿਰ ਨੂੰ ਮੀਂਹ ਪਵੇਗਾ');
      } else if (avgWind > 25) {
        timeDescriptions.add('ਦੁਪਹਿਰ ਨੂੰ ਤੇਜ਼ ਹਵਾ ਚਲੇਗੀ');
      }
    }
    
    return timeDescriptions.join(', ਅਤੇ ');
  }
  
  /// Weather condition translations
  static const Map<int, String> _weatherConditionsPunjabi = {
    1000: 'ਸਾਫ਼',
    1001: 'ਬੱਦਲਵਾਈ',
    1100: 'ਜ਼ਿਆਦਾਤਰ ਸਾਫ਼',
    1101: 'ਅੰਸ਼ਿਕ ਬੱਦਲਵਾਈ',
    1102: 'ਜ਼ਿਆਦਾਤਰ ਬੱਦਲਵਾਈ',
    2000: 'ਧੁੰਦ',
    2100: 'ਹਲਕੀ ਧੁੰਦ',
    4000: 'ਬੂੰਦਾ-ਬਾਂਦੀ',
    4001: 'ਮੀਂਹ',
    4200: 'ਹਲਕਾ ਮੀਂਹ',
    4201: 'ਤੇਜ਼ ਮੀਂਹ',
    5000: 'ਬਰਫ਼',
    5001: 'ਬਰਫ਼ ਦੇ ਫਾਹੇ',
    5100: 'ਹਲਕੀ ਬਰਫ਼',
    5101: 'ਤੇਜ਼ ਬਰਫ਼ਬਾਰੀ',
    8000: 'ਤੂਫਾਨ',
  };
  
  /// Get weather condition name in Punjabi
  static String getWeatherConditionPunjabi(int weatherCode) {
    return _weatherConditionsPunjabi[weatherCode] ?? 'ਅਣਜਾਣ';
  }
}
