import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:location/location.dart';
import '../models/weather_model.dart';

/// Weather service to fetch data from Tomorrow.io API
class WeatherService {
  static const String _baseUrl = 'https://api.tomorrow.io/v4';
  static const String _apiKey = 'MS8kzlNQNGv2Y0t8KBVtAGHW9PPxkA2g'; // Replace with your actual Tomorrow.io API key
  
  final Location _location = Location();

  /// Get current location coordinates
  Future<LocationData?> getCurrentLocation() async {
    try {
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          return null;
        }
      }

      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          return null;
        }
      }

      return await _location.getLocation();
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  /// Fetch weather forecast for today and tomorrow
  Future<List<WeatherForecast>> getWeatherForecast() async {
    try {
    
      final lat = 31.0190;
      final lon = 75.7879;

      // Prepare dates for today and tomorrow
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));

      // Format dates for API
      final todayStr = _formatDateForApi(today);
      final tomorrowStr = _formatDateForApi(tomorrow);

      // Build API URL for forecast
      final url = Uri.parse(
        '$_baseUrl/weather/forecast?location=$lat,$lon&timesteps=1d&startTime=${todayStr}T00:00:00Z&endTime=${tomorrowStr}T23:59:59Z&fields=temperature,temperatureMax,temperatureMin,humidity,windSpeed,windDirection,precipitationProbability,precipitationIntensity,weatherCode,cloudCover,visibility,uvIndex&apikey=$_apiKey'
      );


      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseWeatherData(data);
      } else {
        throw Exception('Failed to load weather data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch weather data: $e');
    }
  }

  /// Get hourly weather data for more detailed information
  Future<List<HourlyWeather>> getHourlyWeather() async {
    try {
      final locationData = await getCurrentLocation();
      if (locationData == null) {
        throw Exception('Unable to get location');
      }

      final lat = locationData.latitude!;
      final lon = locationData.longitude!;

      // Get hourly data for today and tomorrow
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 2));

      final todayStr = _formatDateForApi(today);
      final tomorrowStr = _formatDateForApi(tomorrow);

      final url = Uri.parse(
        '$_baseUrl/weather/forecast?location=$lat,$lon&timesteps=1h&startTime=${todayStr}T00:00:00Z&endTime=${tomorrowStr}T00:00:00Z&fields=temperature,precipitationProbability,windSpeed,weatherCode,humidity&apikey=$_apiKey'
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseHourlyData(data);
      } else {
        throw Exception('Failed to load hourly weather data');
      }
    } catch (e) {
      print('Error fetching hourly weather: $e');
      return [];
    }
  }

  /// Parse the weather forecast data from API response
  List<WeatherForecast> _parseWeatherData(Map<String, dynamic> data) {
    final List<WeatherForecast> forecasts = [];
    
    if (data['timelines'] != null && data['timelines']['daily'] != null) {
      final timelines = data['timelines']['daily'] as List;
      
      for (final interval in timelines) {
        final startTime = DateTime.parse(interval['time']);
        final forecast = WeatherForecast.fromJson(interval, startTime);
        forecasts.add(forecast);
      }
    }

    // The API call already limits to today and tomorrow, but we sort to be safe.
    forecasts.sort((a, b) => a.date.compareTo(b.date));
    return forecasts.take(2).toList();
  }

  /// Parse hourly weather data
  List<HourlyWeather> _parseHourlyData(Map<String, dynamic> data) {
    final List<HourlyWeather> hourlyData = [];
    
    if (data['data'] != null && data['data']['timelines'] != null) {
      final timelines = data['data']['timelines'] as List;
      
      for (final timeline in timelines) {
        if (timeline['timestep'] == '1h' && timeline['intervals'] != null) {
          final intervals = timeline['intervals'] as List;
          
          for (final interval in intervals) {
            final hourly = HourlyWeather.fromJson(interval);
            hourlyData.add(hourly);
          }
        }
      }
    }

    return hourlyData;
  }

  /// Format date for API request
  String _formatDateForApi(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Get weather icon based on weather code and time
  String getWeatherIcon(int weatherCode, {bool isDay = true}) {
    switch (weatherCode) {
      case 1000: // Clear
        return isDay ? '☀️' : '🌙';
      case 1001: // Cloudy
        return '☁️';
      case 1100: // Mostly Clear
        return isDay ? '🌤️' : '🌙';
      case 1101: // Partly Cloudy
        return isDay ? '⛅' : '☁️';
      case 1102: // Mostly Cloudy
        return '☁️';
      case 2000: // Fog
      case 2100: // Light Fog
        return '🌫️';
      case 4000: // Drizzle
      case 4200: // Light Rain
        return '🌦️';
      case 4001: // Rain
      case 4201: // Heavy Rain
        return '🌧️';
      case 5000: // Snow
      case 5001: // Flurries
      case 5100: // Light Snow
      case 5101: // Heavy Snow
        return '❄️';
      case 8000: // Thunderstorm
        return '⛈️';
      default:
        return isDay ? '☀️' : '🌙';
    }
  }
}
