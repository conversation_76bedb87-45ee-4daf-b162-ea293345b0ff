import '../models/weather_model.dart';

/// Utility functions for weather data processing
class WeatherUtils {
  
  /// Convert temperature from Celsius to Fahrenheit
  static double celsiusToFahrenheit(double celsius) {
    return (celsius * 9 / 5) + 32;
  }

  /// Convert wind speed from km/h to mph
  static double kmhToMph(double kmh) {
    return kmh * 0.621371;
  }

  /// Convert wind speed from m/s to km/h
  static double msToKmh(double ms) {
    return ms * 3.6;
  }

  /// Get weather severity level (0-5, where 5 is most severe)
  static int getWeatherSeverity(WeatherData weather) {
    int severity = 0;
    
    // Temperature extremes
    if (weather.temperature < 0 || weather.temperature > 40) {
      severity += 2;
    } else if (weather.temperature < 5 || weather.temperature > 35) {
      severity += 1;
    }
    
    // High precipitation probability
    if (weather.precipitationProbability > 80) {
      severity += 2;
    } else if (weather.precipitationProbability > 60) {
      severity += 1;
    }
    
    // Strong winds
    if (weather.windSpeed > 50) {
      severity += 2;
    } else if (weather.windSpeed > 30) {
      severity += 1;
    }
    
    // Severe weather conditions
    switch (weather.weatherCode) {
      case 8000: // Thunderstorm
        severity += 3;
        break;
      case 4201: // Heavy Rain
      case 5101: // Heavy Snow
        severity += 2;
        break;
      case 6001: // Freezing Rain
      case 6201: // Heavy Freezing Rain
        severity += 2;
        break;
    }
    
    return severity.clamp(0, 5);
  }

  /// Get comfort level description in Punjabi
  static String getComfortLevel(WeatherData weather) {
    final temp = weather.temperature;
    final humidity = weather.humidity;
    final windSpeed = weather.windSpeed;
    
    // Calculate heat index for comfort
    double comfortScore = 0;
    
    // Temperature comfort (optimal around 20-25°C)
    if (temp >= 18 && temp <= 26) {
      comfortScore += 3;
    } else if (temp >= 15 && temp <= 30) {
      comfortScore += 2;
    } else if (temp >= 10 && temp <= 35) {
      comfortScore += 1;
    }
    
    // Humidity comfort (optimal 40-60%)
    if (humidity >= 40 && humidity <= 60) {
      comfortScore += 2;
    } else if (humidity >= 30 && humidity <= 70) {
      comfortScore += 1;
    }
    
    // Wind comfort (light breeze is good)
    if (windSpeed >= 5 && windSpeed <= 15) {
      comfortScore += 1;
    } else if (windSpeed > 25) {
      comfortScore -= 1;
    }
    
    // Rain reduces comfort
    if (weather.precipitationProbability > 60) {
      comfortScore -= 1;
    }
    
    if (comfortScore >= 5) {
      return 'ਬਹੁਤ ਸੁਹਾਵਣਾ';
    } else if (comfortScore >= 3) {
      return 'ਸੁਹਾਵਣਾ';
    } else if (comfortScore >= 1) {
      return 'ਠੀਕ ਠਾਕ';
    } else {
      return 'ਅਸੁਵਿਧਾਜਨਕ';
    }
  }

  /// Get clothing recommendation in Punjabi
  static String getClothingRecommendation(WeatherData weather) {
    final temp = weather.temperature;
    final rainChance = weather.precipitationProbability;
    final windSpeed = weather.windSpeed;
    
    List<String> recommendations = [];
    
    // Temperature-based clothing
    if (temp < 5) {
      recommendations.add('ਗਰਮ ਕੱਪੜੇ ਪਹਿਨੋ');
    } else if (temp < 15) {
      recommendations.add('ਸਵੈਟਰ ਜਾਂ ਜੈਕਟ ਪਹਿਨੋ');
    } else if (temp < 25) {
      recommendations.add('ਹਲਕੇ ਕੱਪੜੇ ਪਹਿਨੋ');
    } else if (temp > 30) {
      recommendations.add('ਸੂਤੀ ਕੱਪੜੇ ਪਹਿਨੋ');
    }
    
    // Rain protection
    if (rainChance > 60) {
      recommendations.add('ਛਤਰੀ ਲੈ ਕੇ ਜਾਓ');
    } else if (rainChance > 30) {
      recommendations.add('ਛਤਰੀ ਸਾਥ ਰੱਖੋ');
    }
    
    // Wind protection
    if (windSpeed > 25) {
      recommendations.add('ਹਵਾ ਤੋਂ ਬਚਾਅ ਕਰੋ');
    }
    
    return recommendations.isEmpty 
        ? 'ਆਮ ਕੱਪੜੇ ਪਹਿਨੋ' 
        : recommendations.join(', ');
  }

  /// Get activity recommendation in Punjabi
  static String getActivityRecommendation(WeatherData weather) {
    final temp = weather.temperature;
    final rainChance = weather.precipitationProbability;
    final windSpeed = weather.windSpeed;
    final weatherCode = weather.weatherCode;
    
    // Bad weather conditions
    if (rainChance > 70 || windSpeed > 40 || weatherCode == 8000) {
      return 'ਘਰ ਵਿੱਚ ਰਹੋ';
    }
    
    // Extreme temperatures
    if (temp < 0 || temp > 40) {
      return 'ਬਾਹਰ ਜਾਣ ਤੋਂ ਬਚੋ';
    }
    
    // Good weather for activities
    if (temp >= 15 && temp <= 30 && rainChance < 30 && windSpeed < 20) {
      return 'ਬਾਹਰ ਘੁੰਮਣ ਜਾ ਸਕਦੇ ਹੋ';
    }
    
    // Moderate weather
    if (rainChance < 50) {
      return 'ਸਾਵਧਾਨੀ ਨਾਲ ਬਾਹਰ ਜਾਓ';
    }
    
    return 'ਘਰ ਵਿੱਚ ਰਹਿਣਾ ਬਿਹਤਰ ਹੈ';
  }

  /// Format temperature with appropriate unit
  static String formatTemperature(double temp, {bool useFahrenheit = false}) {
    if (useFahrenheit) {
      return '${celsiusToFahrenheit(temp).round()}°F';
    }
    return '${temp.round()}°C';
  }

  /// Format wind speed with direction
  static String formatWind(double speed, String direction) {
    return '${speed.round()} km/h $direction';
  }

  /// Get UV index description in Punjabi
  static String getUVDescription(double uvIndex) {
    if (uvIndex <= 2) {
      return 'ਘੱਟ UV';
    } else if (uvIndex <= 5) {
      return 'ਮੱਧਮ UV';
    } else if (uvIndex <= 7) {
      return 'ਉੱਚ UV';
    } else if (uvIndex <= 10) {
      return 'ਬਹੁਤ ਉੱਚ UV';
    } else {
      return 'ਖਤਰਨਾਕ UV';
    }
  }

  /// Get visibility description in Punjabi
  static String getVisibilityDescription(double visibility) {
    if (visibility >= 10) {
      return 'ਸਾਫ਼ ਦਿਖਾਈ';
    } else if (visibility >= 5) {
      return 'ਚੰਗੀ ਦਿਖਾਈ';
    } else if (visibility >= 1) {
      return 'ਘੱਟ ਦਿਖਾਈ';
    } else {
      return 'ਬਹੁਤ ਘੱਟ ਦਿਖਾਈ';
    }
  }

  /// Check if it's a good day for specific activities
  static Map<String, bool> getActivitySuitability(WeatherData weather) {
    final temp = weather.temperature;
    final rainChance = weather.precipitationProbability;
    final windSpeed = weather.windSpeed;
    
    return {
      'outdoor_sports': temp >= 10 && temp <= 35 && rainChance < 30 && windSpeed < 25,
      'picnic': temp >= 15 && temp <= 30 && rainChance < 20 && windSpeed < 20,
      'beach': temp >= 20 && temp <= 35 && rainChance < 20 && windSpeed < 15,
      'hiking': temp >= 5 && temp <= 30 && rainChance < 40 && windSpeed < 30,
      'cycling': temp >= 10 && temp <= 32 && rainChance < 30 && windSpeed < 25,
      'gardening': temp >= 10 && temp <= 35 && rainChance < 50,
    };
  }

  /// Get time-based weather description
  static String getTimeBasedDescription(DateTime time, WeatherData weather) {
    final hour = time.hour;
    String timeOfDay;
    
    if (hour >= 5 && hour < 12) {
      timeOfDay = 'ਸਵੇਰੇ';
    } else if (hour >= 12 && hour < 17) {
      timeOfDay = 'ਦੁਪਹਿਰ';
    } else if (hour >= 17 && hour < 21) {
      timeOfDay = 'ਸ਼ਾਮ';
    } else {
      timeOfDay = 'ਰਾਤ';
    }
    
    return '$timeOfDay ${weather.temperature.round()}°C';
  }
}
