import 'package:flutter/material.dart';

/// Animated weather icon widget with beautiful animations
class AnimatedWeatherIcon extends StatefulWidget {
  final int weatherCode;
  final double size;
  final bool isDay;

  const AnimatedWeatherIcon({
    super.key,
    required this.weatherCode,
    this.size = 80.0,
    this.isDay = true,
  });

  @override
  State<AnimatedWeatherIcon> createState() => _AnimatedWeatherIconState();
}

class _AnimatedWeatherIconState extends State<AnimatedWeatherIcon>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _bounceController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Initialize animations
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    // Start appropriate animations based on weather
    _startAnimations();
  }

  void _startAnimations() {
    // Start bounce animation once
    _bounceController.forward();
    
    // Start continuous animations based on weather type
    switch (widget.weatherCode) {
      case 1000: // Clear - gentle pulse for sun
        _pulseController.repeat(reverse: true);
        if (widget.isDay) {
          _rotationController.repeat();
        }
        break;
      case 1001: // Cloudy - gentle pulse
      case 1101: // Partly Cloudy
      case 1102: // Mostly Cloudy
        _pulseController.repeat(reverse: true);
        break;
      case 4001: // Rain - faster pulse
      case 4200: // Light Rain
      case 4201: // Heavy Rain
        _pulseController.duration = const Duration(milliseconds: 800);
        _pulseController.repeat(reverse: true);
        break;
      case 8000: // Thunderstorm - rapid pulse
        _pulseController.duration = const Duration(milliseconds: 500);
        _pulseController.repeat(reverse: true);
        break;
      default:
        _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _pulseAnimation,
        _bounceAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _bounceAnimation.value * _pulseAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.size / 2),
                boxShadow: [
                  BoxShadow(
                    color: _getGlowColor().withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  _getWeatherEmoji(),
                  style: TextStyle(
                    fontSize: widget.size * 0.6,
                    shadows: [
                      Shadow(
                        color: _getGlowColor().withOpacity(0.5),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Get weather emoji based on weather code
  String _getWeatherEmoji() {
    switch (widget.weatherCode) {
      case 1000: // Clear
        return widget.isDay ? '☀️' : '🌙';
      case 1001: // Cloudy
        return '☁️';
      case 1100: // Mostly Clear
        return widget.isDay ? '🌤️' : '🌙';
      case 1101: // Partly Cloudy
        return widget.isDay ? '⛅' : '☁️';
      case 1102: // Mostly Cloudy
        return '☁️';
      case 2000: // Fog
      case 2100: // Light Fog
        return '🌫️';
      case 3000: // Light Wind
      case 3001: // Wind
      case 3002: // Strong Wind
        return '💨';
      case 4000: // Drizzle
      case 4200: // Light Rain
        return '🌦️';
      case 4001: // Rain
      case 4201: // Heavy Rain
        return '🌧️';
      case 5000: // Snow
      case 5001: // Flurries
      case 5100: // Light Snow
      case 5101: // Heavy Snow
        return '❄️';
      case 6000: // Freezing Drizzle
      case 6001: // Freezing Rain
      case 6200: // Light Freezing Rain
      case 6201: // Heavy Freezing Rain
        return '🧊';
      case 7000: // Ice Pellets
      case 7101: // Heavy Ice Pellets
      case 7102: // Light Ice Pellets
        return '🧊';
      case 8000: // Thunderstorm
        return '⛈️';
      default:
        return widget.isDay ? '☀️' : '🌙';
    }
  }

  /// Get glow color based on weather condition
  Color _getGlowColor() {
    switch (widget.weatherCode) {
      case 1000: // Clear
        return widget.isDay ? Colors.orange : Colors.blue;
      case 1001: // Cloudy
      case 1101: // Partly Cloudy
      case 1102: // Mostly Cloudy
        return Colors.grey;
      case 2000: // Fog
      case 2100: // Light Fog
        return Colors.grey.shade300;
      case 4000: // Drizzle
      case 4001: // Rain
      case 4200: // Light Rain
      case 4201: // Heavy Rain
        return Colors.blue;
      case 5000: // Snow
      case 5001: // Flurries
      case 5100: // Light Snow
      case 5101: // Heavy Snow
        return Colors.lightBlue.shade100;
      case 8000: // Thunderstorm
        return Colors.purple;
      default:
        return Colors.orange;
    }
  }
}

/// Weather background gradient widget
class WeatherBackground extends StatelessWidget {
  final int weatherCode;
  final bool isDay;
  final Widget child;

  const WeatherBackground({
    super.key,
    required this.weatherCode,
    required this.isDay,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: _getGradientColors(),
        ),
      ),
      child: child,
    );
  }

  /// Get gradient colors based on weather and time
  List<Color> _getGradientColors() {
    if (isDay) {
      switch (weatherCode) {
        case 1000: // Clear day
          return [
            const Color(0xFF87CEEB), // Sky blue
            const Color(0xFFE0F6FF), // Light blue
          ];
        case 4001: // Rain
        case 4200: // Light Rain
        case 4201: // Heavy Rain
          return [
            const Color(0xFF708090), // Slate gray
            const Color(0xFFB0C4DE), // Light steel blue
          ];
        case 8000: // Thunderstorm
          return [
            const Color(0xFF2F4F4F), // Dark slate gray
            const Color(0xFF696969), // Dim gray
          ];
        case 5000: // Snow
        case 5100: // Light Snow
        case 5101: // Heavy Snow
          return [
            const Color(0xFFF0F8FF), // Alice blue
            const Color(0xFFE6E6FA), // Lavender
          ];
        default: // Cloudy
          return [
            const Color(0xFF87CEEB), // Sky blue
            const Color(0xFFD3D3D3), // Light gray
          ];
      }
    } else {
      // Night gradients
      switch (weatherCode) {
        case 1000: // Clear night
          return [
            const Color(0xFF191970), // Midnight blue
            const Color(0xFF483D8B), // Dark slate blue
          ];
        case 4001: // Rain night
        case 4200:
        case 4201:
          return [
            const Color(0xFF2F4F4F), // Dark slate gray
            const Color(0xFF4682B4), // Steel blue
          ];
        default: // Cloudy night
          return [
            const Color(0xFF2F4F4F), // Dark slate gray
            const Color(0xFF708090), // Slate gray
          ];
      }
    }
  }
}
