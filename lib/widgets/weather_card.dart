import 'package:flutter/material.dart';
import '../models/weather_model.dart';
import '../services/punjabi_translator.dart';
import 'animated_weather_icon.dart';

/// Beautiful weather card widget for displaying daily weather information
class WeatherCard extends StatefulWidget {
  final WeatherForecast forecast;
  final bool isToday;

  const WeatherCard({
    super.key,
    required this.forecast,
    this.isToday = false,
  });

  @override
  State<WeatherCard> createState() => _WeatherCardState();
}

class _WeatherCardState extends State<WeatherCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    // Start animation with delay for staggered effect
    Future.delayed(Duration(milliseconds: widget.isToday ? 0 : 200), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: _getCardGradient(),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Stack(
                  children: [
                    // Background pattern
                    _buildBackgroundPattern(),
                    // Main content
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(),
                          const SizedBox(height: 16),
                          _buildMainWeatherInfo(),
                          const SizedBox(height: 16),
                          _buildWeatherDescription(),
                          const SizedBox(height: 12),
                          _buildWeatherDetails(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build card header with day name and date
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.forecast.dayName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black26,
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
            Text(
              _formatDate(widget.forecast.date),
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
        if (widget.isToday)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: const Text(
              'ਅੱਜ',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// Build main weather information with icon and temperature
  Widget _buildMainWeatherInfo() {
    return Row(
      children: [
        AnimatedWeatherIcon(
          weatherCode: widget.forecast.weatherData.weatherCode,
          size: 80,
          isDay: _isDay(),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${widget.forecast.weatherData.temperature.round()}°C',
                style: const TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
              Text(
                PunjabiTranslator.getWeatherConditionPunjabi(
                  widget.forecast.weatherData.weatherCode,
                ),
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              Text(
                '${widget.forecast.weatherData.temperatureMin.round()}° / ${widget.forecast.weatherData.temperatureMax.round()}°',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build weather description in Punjabi
  Widget _buildWeatherDescription() {
    final description = PunjabiTranslator.generateWeatherDescription(widget.forecast);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Text(
        description,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.white,
          height: 1.4,
        ),
      ),
    );
  }

  /// Build detailed weather information
  Widget _buildWeatherDetails() {
    final weather = widget.forecast.weatherData;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildDetailItem(
          icon: Icons.water_drop,
          label: 'ਮੀਂਹ',
          value: '${weather.precipitationProbability.round()}%',
        ),
        _buildDetailItem(
          icon: Icons.air,
          label: 'ਹਵਾ',
          value: '${weather.windDirectionAbbr} ${weather.windSpeed.round()} km/h',
        ),
        _buildDetailItem(
          icon: Icons.opacity,
          label: 'ਨਮੀ',
          value: '${weather.humidity.round()}%',
        ),
        _buildDetailItem(
          icon: Icons.visibility,
          label: 'ਦ੍ਰਿਸ਼ਟੀ',
          value: weather.visibility.toString(),
        ),
      ],
    );
  }

  /// Build individual detail item
  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white.withOpacity(0.8),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// Build background pattern for visual appeal
  Widget _buildBackgroundPattern() {
    return Positioned.fill(
      child: CustomPaint(
        painter: WeatherPatternPainter(
          weatherCode: widget.forecast.weatherData.weatherCode,
        ),
      ),
    );
  }

  /// Get card gradient colors based on weather
  List<Color> _getCardGradient() {
    final weatherCode = widget.forecast.weatherData.weatherCode;
    
    switch (weatherCode) {
      case 1000: // Clear
        return _isDay()
            ? [const Color(0xFF4A90E2), const Color(0xFF7BB3F0)]
            : [const Color(0xFF2C3E50), const Color(0xFF4A6741)];
      case 4001: // Rain
      case 4200:
      case 4201:
        return [const Color(0xFF5D737E), const Color(0xFF8FA8B2)];
      case 8000: // Thunderstorm
        return [const Color(0xFF2C3E50), const Color(0xFF34495E)];
      case 5000: // Snow
      case 5100:
      case 5101:
        return [const Color(0xFFB8C6DB), const Color(0xFFF5F7FA)];
      default: // Cloudy
        return [const Color(0xFF6C7B7F), const Color(0xFF99A3A4)];
    }
  }

  /// Check if it's day time
  bool _isDay() {
    final hour = DateTime.now().hour;
    return hour >= 6 && hour < 18;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final months = [
      'ਜਨਵਰੀ', 'ਫਰਵਰੀ', 'ਮਾਰਚ', 'ਅਪ੍ਰੈਲ', 'ਮਈ', 'ਜੂਨ',
      'ਜੁਲਾਈ', 'ਅਗਸਤ', 'ਸਤੰਬਰ', 'ਅਕਤੂਬਰ', 'ਨਵੰਬਰ', 'ਦਸੰਬਰ'
    ];
    final monthIndex = (date.month - 1).clamp(0, 11);
    return '${date.day} ${months[monthIndex]}';
  }
}

/// Custom painter for weather background patterns
class WeatherPatternPainter extends CustomPainter {
  final int weatherCode;

  WeatherPatternPainter({required this.weatherCode});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 1;

    switch (weatherCode) {
      case 4001: // Rain - draw rain lines
      case 4200:
      case 4201:
        _drawRainPattern(canvas, size, paint);
        break;
      case 5000: // Snow - draw snowflakes
      case 5100:
      case 5101:
        _drawSnowPattern(canvas, size, paint);
        break;
      default:
        _drawCloudPattern(canvas, size, paint);
    }
  }

  void _drawRainPattern(Canvas canvas, Size size, Paint paint) {
    for (int i = 0; i < 20; i++) {
      final x = (size.width / 20) * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x - 10, size.height),
        paint,
      );
    }
  }

  void _drawSnowPattern(Canvas canvas, Size size, Paint paint) {
    for (int i = 0; i < 15; i++) {
      final x = (size.width / 15) * i;
      final y = (size.height / 10) * (i % 10);
      canvas.drawCircle(Offset(x, y), 2, paint);
    }
  }

  void _drawCloudPattern(Canvas canvas, Size size, Paint paint) {
    final path = Path();
    for (int i = 0; i < 5; i++) {
      final y = (size.height / 5) * i;
      path.moveTo(0, y);
      path.quadraticBezierTo(size.width / 2, y - 10, size.width, y);
    }
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
