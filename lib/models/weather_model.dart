/// Weather data models for the Akaal Weather app
/// Contains models for weather forecast data from Tomorrow.io API

class WeatherForecast {
  final DateTime date;
  final String dayName;
  final WeatherData weatherData;
  final List<HourlyWeather> hourlyData;

  WeatherForecast({
    required this.date,
    required this.dayName,
    required this.weatherData,
    required this.hourlyData,
  });

  factory WeatherForecast.fromJson(Map<String, dynamic> json, DateTime date) {
    return WeatherForecast(
      date: date,
      dayName: _getDayName(date),
      weatherData: WeatherData.fromJson(json['values'] ?? {}),
      hourlyData: [], // Will be populated separately
    );
  }

  static String _getDayName(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    if (targetDate == today) {
      return 'ਅੱਜ'; // Today in Punjabi
    } else if (targetDate == today.add(const Duration(days: 1))) {
      return 'ਕੱਲ੍ਹ'; // Tomorrow in Punjabi
    }
    return 'ਅਗਲਾ ਦਿਨ'; // Next day in Punjabi
  }
}

class WeatherData {
  final double temperature;
  final double temperatureMax;
  final double temperatureMin;
  final double humidity;
  final double windSpeed;
  final double windDirection;
  final double precipitationProbability;
  final double precipitationIntensity;
  final int weatherCode;
  final double cloudCover;
  final double visibility;
  final double uvIndex;

  WeatherData({
    required this.temperature,
    required this.temperatureMax,
    required this.temperatureMin,
    required this.humidity,
    required this.windSpeed,
    required this.windDirection,
    required this.precipitationProbability,
    required this.precipitationIntensity,
    required this.weatherCode,
    required this.cloudCover,
    required this.visibility,
    required this.uvIndex,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      temperature: (json['temperatureAvg'] ?? 0).toDouble(),
      temperatureMax: (json['temperatureMax'] ?? 0).toDouble(),
      temperatureMin: (json['temperatureMin'] ?? 0).toDouble(),
      humidity: (json['humidityAvg'] ?? 0).toDouble(),
      windSpeed: (json['windSpeedAvg'] ?? 0).toDouble(),
      windDirection: (json['windDirectionAvg'] ?? 0).toDouble(),
      precipitationProbability: (json['precipitationProbabilityAvg'] ?? 0).toDouble(),
      precipitationIntensity: (json['rainIntensityAvg'] ?? 0).toDouble(),
      weatherCode: json['weatherCodeMax'] ?? 0,
      cloudCover: (json['cloudCoverAvg'] ?? 0).toDouble(),
      visibility: (json['visibilityAvg'] ?? 0).toDouble(),
      uvIndex: (json['uvIndexAvg'] ?? 0).toDouble(),
    );
  }

  /// Get wind direction in Punjabi
  String get windDirectionPunjabi {
    if (windDirection >= 337.5 || windDirection < 22.5) return 'ਉੱਤਰ'; // North
    if (windDirection >= 22.5 && windDirection < 67.5) return 'ਉੱਤਰ-ਪੂਰਬ'; // Northeast
    if (windDirection >= 67.5 && windDirection < 112.5) return 'ਪੂਰਬ'; // East
    if (windDirection >= 112.5 && windDirection < 157.5) return 'ਦੱਖਣ-ਪੂਰਬ'; // Southeast
    if (windDirection >= 157.5 && windDirection < 202.5) return 'ਦੱਖਣ'; // South
    if (windDirection >= 202.5 && windDirection < 247.5) return 'ਦੱਖਣ-ਪੱਛਮ'; // Southwest
    if (windDirection >= 247.5 && windDirection < 292.5) return 'ਪੱਛਮ'; // West
    return 'ਉੱਤਰ-ਪੱਛਮ'; // Northwest
  }

  /// Get wind direction abbreviation in English for display
  String get windDirectionAbbr {
    if (windDirection >= 337.5 || windDirection < 22.5) return 'N';
    if (windDirection >= 22.5 && windDirection < 67.5) return 'NE';
    if (windDirection >= 67.5 && windDirection < 112.5) return 'E';
    if (windDirection >= 112.5 && windDirection < 157.5) return 'SE';
    if (windDirection >= 157.5 && windDirection < 202.5) return 'S';
    if (windDirection >= 202.5 && windDirection < 247.5) return 'SW';
    if (windDirection >= 247.5 && windDirection < 292.5) return 'W';
    return 'NW';
  }
}

class HourlyWeather {
  final DateTime time;
  final double temperature;
  final double precipitationProbability;
  final double windSpeed;
  final int weatherCode;
  final double humidity;

  HourlyWeather({
    required this.time,
    required this.temperature,
    required this.precipitationProbability,
    required this.windSpeed,
    required this.weatherCode,
    required this.humidity,
  });

  factory HourlyWeather.fromJson(Map<String, dynamic> json) {
    final values = json['values'] ?? {};
    return HourlyWeather(
      time: DateTime.parse(json['time'] ?? DateTime.now().toIso8601String()),
      temperature: (values['temperature'] ?? 0).toDouble(),
      precipitationProbability: (values['precipitationProbability'] ?? 0).toDouble(),
      windSpeed: (values['windSpeed'] ?? 0).toDouble(),
      weatherCode: values['weatherCode'] ?? 0,
      humidity: (values['humidity'] ?? 0).toDouble(),
    );
  }
}

/// Weather condition codes mapping to descriptions
class WeatherCondition {
  static const Map<int, String> conditions = {
    0: 'Unknown',
    1000: 'Clear',
    1001: 'Cloudy',
    1100: 'Mostly Clear',
    1101: 'Partly Cloudy',
    1102: 'Mostly Cloudy',
    2000: 'Fog',
    2100: 'Light Fog',
    3000: 'Light Wind',
    3001: 'Wind',
    3002: 'Strong Wind',
    4000: 'Drizzle',
    4001: 'Rain',
    4200: 'Light Rain',
    4201: 'Heavy Rain',
    5000: 'Snow',
    5001: 'Flurries',
    5100: 'Light Snow',
    5101: 'Heavy Snow',
    6000: 'Freezing Drizzle',
    6001: 'Freezing Rain',
    6200: 'Light Freezing Rain',
    6201: 'Heavy Freezing Rain',
    7000: 'Ice Pellets',
    7101: 'Heavy Ice Pellets',
    7102: 'Light Ice Pellets',
    8000: 'Thunderstorm',
  };

  static String getCondition(int code) {
    return conditions[code] ?? 'Unknown';
  }
}
