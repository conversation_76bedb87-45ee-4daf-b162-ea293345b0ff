import 'package:flutter/material.dart';
import '../models/weather_model.dart';
import '../services/weather_service.dart';
import '../widgets/weather_card.dart';
import '../widgets/animated_weather_icon.dart';

/// Main weather screen displaying today and tomorrow's weather
class WeatherScreen extends StatefulWidget {
  const WeatherScreen({super.key});

  @override
  State<WeatherScreen> createState() => _WeatherScreenState();
}

class _WeatherScreenState extends State<WeatherScreen>
    with TickerProviderStateMixin {
  final WeatherService _weatherService = WeatherService();
  List<WeatherForecast> _forecasts = [];
  bool _isLoading = true;
  String? _error;
  
  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;

  @override
  void initState() {
    super.initState();
    
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _refreshAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _refreshController,
      curve: Curves.easeInOut,
    ));
    
    _loadWeatherData();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// Load weather data from API
  Future<void> _loadWeatherData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final forecasts = await _weatherService.getWeatherForecast();
      setState(() {
        _forecasts = forecasts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// Refresh weather data with animation
  Future<void> _refreshWeatherData() async {
    _refreshController.forward();
    await _loadWeatherData();
    _refreshController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? _buildLoadingScreen()
          : _error != null
              ? _buildErrorScreen()
              : _buildWeatherContent(),
    );
  }

  /// Build loading screen with animated weather icon
  Widget _buildLoadingScreen() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF4A90E2),
            Color(0xFF7BB3F0),
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedWeatherIcon(
              weatherCode: 1000,
              size: 100,
            ),
            SizedBox(height: 24),
            Text(
              'ਮੌਸਮ ਦੀ ਜਾਣਕਾਰੀ ਲੋਡ ਹੋ ਰਹੀ ਹੈ...',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 16),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  /// Build error screen
  Widget _buildErrorScreen() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF5D737E),
            Color(0xFF8FA8B2),
          ],
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.white,
              ),
              const SizedBox(height: 24),
              const Text(
                'ਮੌਸਮ ਦੀ ਜਾਣਕਾਰੀ ਲੋਡ ਨਹੀਂ ਹੋ ਸਕੀ',
                style: TextStyle(
                  fontSize: 20,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                _error ?? 'ਅਣਜਾਣ ਗਲਤੀ',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.8),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _loadWeatherData,
                icon: const Icon(Icons.refresh),
                label: const Text('ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF5D737E),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build main weather content
  Widget _buildWeatherContent() {
    if (_forecasts.isEmpty) {
      return _buildErrorScreen();
    }

    final currentWeather = _forecasts.first;
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: _getBackgroundGradient(currentWeather.weatherData.weatherCode),
        ),
      ),
      child: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 20),
                ..._forecasts.asMap().entries.map((entry) {
                  final index = entry.key;
                  final forecast = entry.value;
                  return WeatherCard(
                    forecast: forecast,
                    isToday: index == 0,
                  );
                }).toList(),
                const SizedBox(height: 20),
                _buildLastUpdated(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build custom app bar
  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'ਅਕਾਲ ਮੌਸਮ',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black26,
                blurRadius: 2,
              ),
            ],
          ),
        ),
        centerTitle: true,
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.3),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
      actions: [
        AnimatedBuilder(
          animation: _refreshAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _refreshAnimation.value * 2 * 3.14159,
              child: IconButton(
                onPressed: _refreshWeatherData,
                icon: const Icon(
                  Icons.refresh,
                  color: Colors.white,
                ),
                tooltip: 'ਤਾਜ਼ਾ ਕਰੋ',
              ),
            );
          },
        ),
      ],
    );
  }

  /// Build last updated timestamp
  Widget _buildLastUpdated() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            size: 16,
            color: Colors.white.withOpacity(0.8),
          ),
          const SizedBox(width: 8),
          Text(
            'ਆਖਰੀ ਅਪਡੇਟ: ${_formatTime(DateTime.now())}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// Check if it's day time
  bool _isDay() {
    final hour = DateTime.now().hour;
    return hour >= 6 && hour < 18;
  }

  /// Format time for display
  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'ਸ਼ਾਮ' : 'ਸਵੇਰੇ';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  /// Get background gradient colors based on weather
  List<Color> _getBackgroundGradient(int weatherCode) {
    if (_isDay()) {
      switch (weatherCode) {
        case 1000: // Clear day
          return [
            const Color(0xFF87CEEB), // Sky blue
            const Color(0xFFE0F6FF), // Light blue
          ];
        case 4001: // Rain
        case 4200: // Light Rain
        case 4201: // Heavy Rain
          return [
            const Color(0xFF708090), // Slate gray
            const Color(0xFFB0C4DE), // Light steel blue
          ];
        case 8000: // Thunderstorm
          return [
            const Color(0xFF2F4F4F), // Dark slate gray
            const Color(0xFF696969), // Dim gray
          ];
        case 5000: // Snow
        case 5100: // Light Snow
        case 5101: // Heavy Snow
          return [
            const Color(0xFFF0F8FF), // Alice blue
            const Color(0xFFE6E6FA), // Lavender
          ];
        default: // Cloudy
          return [
            const Color(0xFF87CEEB), // Sky blue
            const Color(0xFFD3D3D3), // Light gray
          ];
      }
    } else {
      // Night gradients
      switch (weatherCode) {
        case 1000: // Clear night
          return [
            const Color(0xFF191970), // Midnight blue
            const Color(0xFF483D8B), // Dark slate blue
          ];
        case 4001: // Rain night
        case 4200:
        case 4201:
          return [
            const Color(0xFF2F4F4F), // Dark slate gray
            const Color(0xFF4682B4), // Steel blue
          ];
        default: // Cloudy night
          return [
            const Color(0xFF2F4F4F), // Dark slate gray
            const Color(0xFF708090), // Slate gray
          ];
      }
    }
  }
}
